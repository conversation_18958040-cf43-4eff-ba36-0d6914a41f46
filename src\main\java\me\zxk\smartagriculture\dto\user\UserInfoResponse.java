package me.zxk.smartagriculture.dto.user;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户信息响应DTO
 */
@Data
public class UserInfoResponse {

    private Long id;
    private String username;
    private String nickname;
    private String avatarUrl;
    private String phone;
    private String email;
    private Integer level;
    private Integer experience;
    private FarmStats farmStats;

    @Data
    public static class FarmStats {
        private BigDecimal totalArea;
        private Integer cropCount;
        private Integer harvestCount;

        public FarmStats(BigDecimal totalArea, Integer cropCount, Integer harvestCount) {
            this.totalArea = totalArea;
            this.cropCount = cropCount;
            this.harvestCount = harvestCount;
        }
    }
}
