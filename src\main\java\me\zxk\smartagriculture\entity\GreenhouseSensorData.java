package me.zxk.smartagriculture.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 大棚传感器数据实体类
 */
@Entity
@Table(name = "greenhouse_sensor_data")
@Data
@EqualsAndHashCode(callSuper = false)
public class GreenhouseSensorData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "greenhouse_id", nullable = false)
    private Long greenhouseId;

    @Column(name = "temperature", precision = 5, scale = 2)
    private BigDecimal temperature;

    @Column(name = "humidity", precision = 5, scale = 2)
    private BigDecimal humidity;

    @Column(name = "light_intensity", precision = 10, scale = 2)
    private BigDecimal lightIntensity;

    @Column(name = "soil_humidity", precision = 5, scale = 2)
    private BigDecimal soilHumidity;

    @CreationTimestamp
    @Column(name = "recorded_at", updatable = false)
    private LocalDateTime recordedAt;

    // 多对一关系：多个传感器数据属于一个大棚
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "greenhouse_id", insertable = false, updatable = false)
    private Greenhouse greenhouse;
}
