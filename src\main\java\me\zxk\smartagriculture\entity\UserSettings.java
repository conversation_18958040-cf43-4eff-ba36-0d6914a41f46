package me.zxk.smartagriculture.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 用户设置实体类
 */
@Entity
@Table(name = "user_settings")
@Data
@EqualsAndHashCode(callSuper = false)
public class UserSettings {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", unique = true, nullable = false)
    private Long userId;

    @Column(name = "dark_mode_enabled")
    private Boolean darkModeEnabled = false;

    @Column(name = "notifications_enabled")
    private Boolean notificationsEnabled = true;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 一对一关系：设置属于一个用户
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
}
