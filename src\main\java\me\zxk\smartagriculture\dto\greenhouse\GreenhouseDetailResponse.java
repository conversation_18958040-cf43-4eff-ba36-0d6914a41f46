package me.zxk.smartagriculture.dto.greenhouse;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 大棚详情响应DTO
 */
@Data
public class GreenhouseDetailResponse {

    private Long id;
    private String name;
    private String crop;
    private BigDecimal area;
    private String type;
    private String remark;
    private String status;
    private BigDecimal temperature;
    private BigDecimal humidity;
    private BigDecimal light;
    private BigDecimal soilHumidity;
    private LocalDateTime createTime;

    public GreenhouseDetailResponse(Long id, String name, String crop, BigDecimal area, String type,
                                   String remark, String status, BigDecimal temperature, BigDecimal humidity,
                                   BigDecimal light, BigDecimal soilHumidity, LocalDateTime createTime) {
        this.id = id;
        this.name = name;
        this.crop = crop;
        this.area = area;
        this.type = type;
        this.remark = remark;
        this.status = status;
        this.temperature = temperature;
        this.humidity = humidity;
        this.light = light;
        this.soilHumidity = soilHumidity;
        this.createTime = createTime;
    }
}
