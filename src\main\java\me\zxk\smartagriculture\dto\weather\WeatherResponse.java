package me.zxk.smartagriculture.dto.weather;

import lombok.Data;

import java.util.List;

/**
 * 天气响应DTO
 */
@Data
public class WeatherResponse {

    private String currentLocation;
    private CurrentWeather currentWeather;
    private AgricultureIndex agricultureIndex;
    private RainForecast rainForecast;
    private List<WeeklyForecast> weeklyForecast;
    private WeatherDetails weatherDetails;

    @Data
    public static class CurrentWeather {
        private Double temperature;
        private String type;
        private Integer high;
        private Integer low;
    }

    @Data
    public static class AgricultureIndex {
        private String planting;
        private String irrigation;
        private String sunlight;
    }

    @Data
    public static class RainForecast {
        private Boolean willRain;
        private Integer time;
        private String intensity;
        private List<Double> chartData;
    }

    @Data
    public static class WeeklyForecast {
        private String day;
        private String date;
        private String type;
        private Integer high;
        private Integer low;
        private String icon;
        private String color;
        private String advice;
    }

    @Data
    public static class WeatherDetails {
        private Integer visibility;
        private Integer humidity;
        private Integer windSpeed;
        private Integer feelsLike;
        private Integer uvIndex;
        private Integer pressure;
    }
}
