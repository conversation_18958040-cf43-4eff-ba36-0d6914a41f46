package me.zxk.smartagriculture.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 大棚实体类
 */
@Entity
@Table(name = "greenhouses")
@Data
@EqualsAndHashCode(callSuper = false)
public class Greenhouse {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "crop", length = 50)
    private String crop;

    @Column(name = "area", precision = 10, scale = 2)
    private BigDecimal area;

    @Column(name = "type", length = 50)
    private String type;

    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    @Column(name = "status", length = 20)
    private String status = "normal";

    @Column(name = "current_temperature", precision = 5, scale = 2)
    private BigDecimal currentTemperature;

    @Column(name = "current_humidity", precision = 5, scale = 2)
    private BigDecimal currentHumidity;

    @Column(name = "current_light", precision = 10, scale = 2)
    private BigDecimal currentLight;

    @Column(name = "current_soil_humidity", precision = 5, scale = 2)
    private BigDecimal currentSoilHumidity;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 多对一关系：多个大棚属于一个用户
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    // 一对多关系：大棚拥有多个传感器数据
    @OneToMany(mappedBy = "greenhouse", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GreenhouseSensorData> sensorDataList;
}
