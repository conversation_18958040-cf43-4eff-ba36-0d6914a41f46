package me.zxk.smartagriculture.dto.greenhouse;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 传感器数据响应DTO
 */
@Data
public class SensorDataResponse {

    private LocalDateTime recordedAt;
    private BigDecimal temperature;
    private BigDecimal humidity;
    private BigDecimal lightIntensity;
    private BigDecimal soilHumidity;

    public SensorDataResponse(LocalDateTime recordedAt, BigDecimal temperature, BigDecimal humidity,
                             BigDecimal lightIntensity, BigDecimal soilHumidity) {
        this.recordedAt = recordedAt;
        this.temperature = temperature;
        this.humidity = humidity;
        this.lightIntensity = lightIntensity;
        this.soilHumidity = soilHumidity;
    }
}
