# 智慧大棚环境监控系统 API 测试文档

## 项目启动

1. 确保MySQL数据库已启动，连接信息：
   - 地址：localhost:3363
   - 用户名：root
   - 密码：123456
   - 数据库：smart_agriculture

2. 运行SQL脚本创建数据库表：
   ```bash
   mysql -h localhost -P 3363 -u root -p123456 < db/create.sql
   ```

3. 启动应用：
   ```bash
   mvn spring-boot:run
   ```

4. 应用启动后访问：http://localhost:8080

## API 测试

### 1. 用户注册
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "123456"
  }'
```

### 2. 用户登录
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

### 3. 获取用户信息（需要登录后的token）
```bash
curl -X GET http://localhost:8080/api/users/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. 获取大棚列表
```bash
curl -X GET http://localhost:8080/api/greenhouses \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 5. 创建大棚
```bash
curl -X POST http://localhost:8080/api/greenhouses \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "新建大棚",
    "crop": "辣椒",
    "area": 25.5,
    "type": "plastic",
    "remark": "测试大棚"
  }'
```

### 6. 获取大棚详情
```bash
curl -X GET http://localhost:8080/api/greenhouses/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 7. 获取传感器数据
```bash
curl -X GET http://localhost:8080/api/greenhouses/1/sensor-data \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 8. 获取天气信息
```bash
curl -X GET "http://localhost:8080/api/weather?latitude=39.9042&longitude=116.4074" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 9. 获取作物选项
```bash
curl -X GET http://localhost:8080/api/crops/options
```

### 10. 获取用户设置
```bash
curl -X GET http://localhost:8080/api/users/me/settings \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 默认测试账户

应用启动时会自动创建一个测试账户：
- 用户名：testuser
- 密码：123456

该账户已包含3个测试大棚和相关数据。

## 功能特性

1. **用户认证**：JWT Token认证
2. **大棚管理**：增删改查操作
3. **传感器数据**：自动生成模拟数据（每5分钟更新）
4. **文件上传**：支持头像上传
5. **天气服务**：提供模拟天气数据
6. **统一响应格式**：所有API返回200状态码，错误信息在响应体中

## 注意事项

1. 所有需要认证的API都需要在请求头中包含JWT Token
2. 传感器数据会自动生成，模拟真实环境数据变化
3. 文件上传功能已实现，上传的文件保存在 `uploads/` 目录
4. 天气数据为模拟数据，实际项目中应接入真实的天气API
