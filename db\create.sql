-- 智慧大棚环境监控系统数据库创建脚本
-- 描述: 包含用户管理、大棚管理、传感器数据和用户设置等核心功能表

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `smart_agriculture`
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE `smart_agriculture`;

-- ====================================
-- 1. 用户信息表 (users)
-- ====================================
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID (主键)',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '哈希后的密码',
  `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号码',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '电子邮箱',
  `level` INT DEFAULT 1 COMMENT '用户等级',
  `experience` INT DEFAULT 0 COMMENT '用户经验值',
  `total_area` DECIMAL(10,2) DEFAULT 0.00 COMMENT '总种植面积 (亩)',
  `crop_count` INT DEFAULT 0 COMMENT '作物品种数量',
  `harvest_count` INT DEFAULT 0 COMMENT '收获次数',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- ====================================
-- 2. 大棚信息表 (greenhouses)
-- ====================================
DROP TABLE IF EXISTS `greenhouses`;
CREATE TABLE `greenhouses` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '大棚ID (主键)',
  `user_id` BIGINT NOT NULL COMMENT '所属用户ID (外键)',
  `name` VARCHAR(100) NOT NULL COMMENT '大棚名称',
  `crop` VARCHAR(50) DEFAULT NULL COMMENT '种植作物',
  `area` DECIMAL(10,2) DEFAULT NULL COMMENT '大棚面积 (平方米)',
  `type` VARCHAR(50) DEFAULT NULL COMMENT '大棚类型 (glass/plastic/solar/multi)',
  `remark` TEXT DEFAULT NULL COMMENT '备注信息',
  `status` VARCHAR(20) DEFAULT 'normal' COMMENT '大棚状态 (normal/warning/error)',
  `current_temperature` DECIMAL(5,2) DEFAULT NULL COMMENT '当前温度 (°C)',
  `current_humidity` DECIMAL(5,2) DEFAULT NULL COMMENT '当前湿度 (%)',
  `current_light` DECIMAL(10,2) DEFAULT NULL COMMENT '当前光照 (lux)',
  `current_soil_humidity` DECIMAL(5,2) DEFAULT NULL COMMENT '当前土壤湿度 (%)',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_greenhouses_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大棚信息表';

-- ====================================
-- 3. 大棚传感器数据表 (greenhouse_sensor_data)
-- ====================================
DROP TABLE IF EXISTS `greenhouse_sensor_data`;
CREATE TABLE `greenhouse_sensor_data` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '数据ID (主键)',
  `greenhouse_id` BIGINT NOT NULL COMMENT '大棚ID (外键)',
  `temperature` DECIMAL(5,2) DEFAULT NULL COMMENT '温度 (°C)',
  `humidity` DECIMAL(5,2) DEFAULT NULL COMMENT '湿度 (%)',
  `light_intensity` DECIMAL(10,2) DEFAULT NULL COMMENT '光照强度 (lux)',
  `soil_humidity` DECIMAL(5,2) DEFAULT NULL COMMENT '土壤湿度 (%)',
  `recorded_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  PRIMARY KEY (`id`),
  KEY `idx_greenhouse_id` (`greenhouse_id`),
  KEY `idx_recorded_at` (`recorded_at`),
  KEY `idx_greenhouse_recorded` (`greenhouse_id`, `recorded_at`),
  CONSTRAINT `fk_sensor_data_greenhouse_id` FOREIGN KEY (`greenhouse_id`) REFERENCES `greenhouses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大棚传感器数据表';

-- ====================================
-- 4. 用户设置表 (user_settings)
-- ====================================
DROP TABLE IF EXISTS `user_settings`;
CREATE TABLE `user_settings` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '设置ID (主键)',
  `user_id` BIGINT NOT NULL COMMENT '用户ID (外键，唯一)',
  `dark_mode_enabled` BOOLEAN DEFAULT FALSE COMMENT '是否启用深色模式',
  `notifications_enabled` BOOLEAN DEFAULT TRUE COMMENT '是否启用消息通知',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_user_settings_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设置表';

-- ====================================
-- 5. 插入示例数据
-- ====================================

-- 插入示例用户数据
INSERT INTO `users` (`username`, `password_hash`, `nickname`, `phone`, `email`, `level`, `experience`, `total_area`, `crop_count`, `harvest_count`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G', '系统管理员', '13800138000', '<EMAIL>', 5, 1000, 100.50, 15, 50),
('farmer01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G', '智农用户', '13900139000', '<EMAIL>', 2, 350, 25.80, 5, 12),
('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G', '测试用户', '13700137000', '<EMAIL>', 1, 100, 10.00, 2, 3);

-- 插入示例大棚数据
INSERT INTO `greenhouses` (`user_id`, `name`, `crop`, `area`, `type`, `remark`, `status`, `current_temperature`, `current_humidity`, `current_light`, `current_soil_humidity`) VALUES
(1, '1号智能温室', '番茄', 50.00, 'glass', '主要用于番茄种植实验', 'normal', 25.5, 65.0, 3500.0, 55.0),
(1, '2号塑料大棚', '黄瓜', 30.00, 'plastic', '黄瓜种植区域', 'warning', 28.0, 70.0, 3200.0, 60.0),
(2, '有机蔬菜棚', '茄子', 20.00, 'solar', '有机蔬菜种植', 'normal', 24.0, 62.0, 3800.0, 52.0),
(2, '实验大棚A', '辣椒', 15.50, 'multi', '辣椒品种试验', 'normal', 26.0, 58.0, 3600.0, 48.0),
(3, '小型试验棚', '生菜', 8.00, 'plastic', '个人试验用', 'normal', 22.0, 68.0, 2800.0, 65.0);

-- 插入示例传感器数据（最近24小时的数据）
INSERT INTO `greenhouse_sensor_data` (`greenhouse_id`, `temperature`, `humidity`, `light_intensity`, `soil_humidity`, `recorded_at`) VALUES
-- 1号大棚数据
(1, 25.2, 64.5, 3450.0, 54.8, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(1, 25.8, 65.2, 3520.0, 55.2, DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
(1, 25.5, 65.0, 3500.0, 55.0, NOW()),
-- 2号大棚数据
(2, 27.5, 69.8, 3180.0, 59.5, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(2, 28.2, 70.5, 3220.0, 60.2, DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
(2, 28.0, 70.0, 3200.0, 60.0, NOW()),
-- 3号大棚数据
(3, 23.8, 61.5, 3750.0, 51.8, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(3, 24.2, 62.2, 3820.0, 52.2, DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
(3, 24.0, 62.0, 3800.0, 52.0, NOW());

-- 插入示例用户设置数据
INSERT INTO `user_settings` (`user_id`, `dark_mode_enabled`, `notifications_enabled`) VALUES
(1, FALSE, TRUE),
(2, TRUE, TRUE),
(3, FALSE, FALSE);

-- ====================================
-- 6. 创建视图和存储过程（可选）
-- ====================================

-- 创建大棚概览视图
CREATE VIEW `v_greenhouse_overview` AS
SELECT
    g.id,
    g.user_id,
    g.name,
    g.crop,
    g.area,
    g.type,
    g.status,
    g.current_temperature,
    g.current_humidity,
    g.current_light,
    g.current_soil_humidity,
    g.created_at,
    u.username,
    u.nickname
FROM `greenhouses` g
LEFT JOIN `users` u ON g.user_id = u.id;

-- 创建用户统计视图
CREATE VIEW `v_user_statistics` AS
SELECT
    u.id,
    u.username,
    u.nickname,
    u.total_area,
    u.crop_count,
    u.harvest_count,
    COUNT(g.id) as greenhouse_count,
    COALESCE(SUM(g.area), 0) as actual_total_area
FROM `users` u
LEFT JOIN `greenhouses` g ON u.id = g.user_id
GROUP BY u.id, u.username, u.nickname, u.total_area, u.crop_count, u.harvest_count;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SELECT 'Database and tables created successfully!' as result;