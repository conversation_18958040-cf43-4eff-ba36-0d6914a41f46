============ PRETEND INSTRUCTIONS ============

所有 API 均返回 200 OK 状态码，所有 API 返回均使用如下格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 返回的数据内容
  }
}
```
错误码与错误信息需要你自行设计，并找一个地方统一放置。
============================

好的，我已经仔细阅读了您提供的前端 Vue 代码。这是一个智慧大棚环境监控系统，主要功能包括用户登录注册、大棚列表展示、大棚信息增删改查、大棚环境数据展示（温度、湿度、光照、土壤湿度）、天气信息展示以及用户个人信息管理等。

基于您的技术栈选择 (Java + SpringBoot + MySQL)，我将为您提供详细的数据库设计和所有相关的 API EndPoint 设计。

## 一、数据库设计 (MySQL)

我们将设计以下几个核心数据表：

1.  **`users` (用户信息表)**: 存储用户账户信息。
2.  **`greenhouses` (大棚信息表)**: 存储用户创建的大棚基本信息。
3.  **`greenhouse_sensor_data` (大棚传感器数据表)**: 存储大棚采集到的各类环境数据（时序数据）。
4.  **`user_settings` (用户设置表)**: 存储用户的个性化设置，如主题、通知等 (前端已体现)。

---

**1. `users` (用户信息表)**

| 字段名                  | 类型          | 约束/备注                                     | 描述             |
| :---------------------- | :------------ | :-------------------------------------------- | :--------------- |
| `id`                    | `BIGINT`      | `PRIMARY KEY`, `AUTO_INCREMENT`               | 用户ID (主键)    |
| `username`              | `VARCHAR(50)` | `UNIQUE`, `NOT NULL`                          | 用户名           |
| `password_hash`         | `VARCHAR(255)`| `NOT NULL`                                    | 哈希后的密码     |
| `nickname`              | `VARCHAR(100)`|                                               | 昵称 (例如: 智农用户) |
| `avatar_url`            | `VARCHAR(255)`|                                               | 头像URL          |
| `phone`                 | `VARCHAR(20)` | `UNIQUE`                                      | 手机号码         |
| `email`                 | `VARCHAR(100)`| `UNIQUE`                                      | 电子邮箱         |
| `level`                 | `INT`         | `DEFAULT 1`                                   | 用户等级         |
| `experience`            | `INT`         | `DEFAULT 0`                                   | 用户经验值       |
| `total_area`            | `DECIMAL(10,2)`| `DEFAULT 0.00`                                | 总种植面积 (亩)  |
| `crop_count`            | `INT`         | `DEFAULT 0`                                   | 作物品种数量     |
| `harvest_count`         | `INT`         | `DEFAULT 0`                                   | 收获次数         |
| `created_at`            | `TIMESTAMP`   | `DEFAULT CURRENT_TIMESTAMP`                   | 创建时间         |
| `updated_at`            | `TIMESTAMP`   | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 更新时间         |

* `password_hash`: 存储加密后的密码，不要明文存储。
* `total_area`, `crop_count`, `harvest_count` 对应前端 "我的" 页面的农业数据概览。这些数据可以根据用户下的大棚信息聚合计算，也可以在此处冗余存储以提高查询效率，具体取决于更新频率和性能需求。为了简化，这里假设在用户表冗余存储，并在更新大棚信息时同步更新这些字段。

---

**2. `greenhouses` (大棚信息表)**

| 字段名                  | 类型             | 约束/备注                                     | 描述             |
| :---------------------- | :--------------- | :-------------------------------------------- | :--------------- |
| `id`                    | `BIGINT`         | `PRIMARY KEY`, `AUTO_INCREMENT`               | 大棚ID (主键)    |
| `user_id`               | `BIGINT`         | `NOT NULL`, `FK (users.id)`                   | 所属用户ID (外键) |
| `name`                  | `VARCHAR(100)`   | `NOT NULL`                                    | 大棚名称         |
| `crop`                  | `VARCHAR(50)`    |                                               | 种植作物         |
| `area`                  | `DECIMAL(10,2)`  |                                               | 大棚面积 (平方米) |
| `type`                  | `VARCHAR(50)`    |                                               | 大棚类型 (例如: 'glass', 'plastic', 'solar', 'multi') |
| `remark`                | `TEXT`           |                                               | 备注信息         |
| `status`                | `VARCHAR(20)`    | `DEFAULT 'normal'`                            | 大棚状态 (例如: 'normal', 'warning', 'error') |
| `current_temperature`   | `DECIMAL(5,2)`   |                                               | 当前温度 (°C)    |
| `current_humidity`      | `DECIMAL(5,2)`   |                                               | 当前湿度 (%)     |
| `current_light`         | `DECIMAL(10,2)`  |                                               | 当前光照 (lux)   |
| `current_soil_humidity` | `DECIMAL(5,2)`   |                                               | 当前土壤湿度 (%)   |
| `created_at`            | `TIMESTAMP`      | `DEFAULT CURRENT_TIMESTAMP`                   | 创建时间         |
| `updated_at`            | `TIMESTAMP`      | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 更新时间         |

* `user_id`: 外键，关联到 `users` 表的 `id`，表示这个大棚属于哪个用户。
* `current_temperature`, `current_humidity`, `current_light`, `current_soil_humidity`: 这些字段存储的是该大棚*最新一次*的环境数据快照，用于在列表页快速展示。详细历史数据存储在 `greenhouse_sensor_data` 表。当有新的传感器数据上报时，应更新这些字段。

---

**3. `greenhouse_sensor_data` (大棚传感器数据表)**

| 字段名         | 类型            | 约束/备注                                     | 描述           |
| :------------- | :-------------- | :-------------------------------------------- | :------------- |
| `id`           | `BIGINT`        | `PRIMARY KEY`, `AUTO_INCREMENT`               | 数据ID (主键)  |
| `greenhouse_id`| `BIGINT`        | `NOT NULL`, `FK (greenhouses.id)`             | 大棚ID (外键)  |
| `temperature`  | `DECIMAL(5,2)`  |                                               | 温度 (°C)      |
| `humidity`     | `DECIMAL(5,2)`  |                                               | 湿度 (%)       |
| `light_intensity`| `DECIMAL(10,2)` |                                               | 光照强度 (lux) |
| `soil_humidity`| `DECIMAL(5,2)`  |                                               | 土壤湿度 (%)   |
| `recorded_at`  | `TIMESTAMP`     | `DEFAULT CURRENT_TIMESTAMP`, `INDEX`          | 记录时间       |

* `greenhouse_id`: 外键，关联到 `greenhouses` 表的 `id`。
* `recorded_at`: 数据采集时间。建议在此字段上创建索引，以加速按时间范围查询。
* 可以在 (`greenhouse_id`, `recorded_at`) 上创建复合索引。

---

**4. `user_settings` (用户设置表)**

| 字段名                  | 类型          | 约束/备注                                     | 描述           |
| :---------------------- | :------------ | :-------------------------------------------- | :------------- |
| `id`                    | `BIGINT`      | `PRIMARY KEY`, `AUTO_INCREMENT`               | 设置ID (主键)  |
| `user_id`               | `BIGINT`      | `UNIQUE`, `NOT NULL`, `FK (users.id)`         | 用户ID (外键，唯一) |
| `dark_mode_enabled`     | `BOOLEAN`     | `DEFAULT FALSE`                               | 是否启用深色模式 |
| `notifications_enabled` | `BOOLEAN`     | `DEFAULT TRUE`                                | 是否启用消息通知 |
| `updated_at`            | `TIMESTAMP`   | `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 更新时间       |

* `user_id`: 外键，与 `users` 表一对一关联。

---

## 二、API EndPoint 设计 (Java SpringBoot)

所有 API 建议以 `/api` 作为前缀。例如 `/api/v1/...` (如果考虑版本管理)。这里暂时省略 `/v1`。

### 1. 认证 (Auth)

* **`POST /api/auth/register`**
    * **描述**: 用户注册。
    * **请求体 (Request Body)**:
        ```json
        {
          "username": "newUser",
          "password": "password123"
        }
        ```
    * **响应 (Response)**:
        * `201 Created`: 注册成功。
            ```json
            {
              "message": "用户注册成功",
              "userId": 123,
              "username": "newUser"
            }
            ```
        * `400 Bad Request`: 请求参数错误 (例如用户名已存在，密码格式不符)。
            ```json
            {
              "error": "用户名已存在"
            }
            ```
    * **备注**: 前端注册表单还有“确认密码”，后端校验 `password` 和 `confirmPassword` (如果前端传的话) 是否一致。

* **`POST /api/auth/login`**
    * **描述**: 用户登录。
    * **请求体 (Request Body)**:
        ```json
        {
          "username": "existingUser",
          "password": "password123"
        }
        ```
    * **响应 (Response)**:
        * `200 OK`: 登录成功。
            ```json
            {
              "token": "your_jwt_token_here", // JWT 令牌
              "userId": 1,
              "username": "existingUser",
              "nickname": "智农用户"
            }
            ```
        * `401 Unauthorized`: 用户名或密码错误。
            ```json
            {
              "error": "用户名或密码错误"
            }
            ```

* **`POST /api/auth/logout`**
    * **描述**: 用户登出。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **响应 (Response)**:
        * `200 OK`: 登出成功。
            ```json
            {
              "message": "登出成功"
            }
            ```
    * **备注**: 后端可能需要将 JWT 加入黑名单或进行其他处理。

### 2. 用户 (Users)

* **`GET /api/users/me`**
    * **描述**: 获取当前登录用户的详细信息 (用于 "我的" 页面)。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **响应 (Response)**:
        * `200 OK`:
            ```json
            {
              "id": 1,
              "username": "currentUser",
              "nickname": "智农用户",
              "avatarUrl": "/path/to/avatar.jpg",
              "phone": "13800138000",
              "email": "<EMAIL>",
              "level": 1,
              "experience": 150,
              "farmStats": { // 对应前端 farmStats
                "totalArea": 15.6, // 从 users 表的 total_area 获取
                "cropCount": 8,    // 从 users 表的 crop_count 获取
                "harvestCount": 23 // 从 users 表的 harvest_count 获取
              }
            }
            ```
        * `401 Unauthorized`: 未登录或 Token 无效。

* **`PUT /api/users/me`**
    * **描述**: 更新当前登录用户的个人信息 (编辑个人信息)。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **请求体 (Request Body)**: (只包含需要更新的字段)
        ```json
        {
          "nickname": "农业小能手",
          "phone": "13900139000",
          "email": "<EMAIL>"
          // avatarUrl 的更新通常通过单独的文件上传接口
        }
        ```
    * **响应 (Response)**:
        * `200 OK`: 更新成功，返回更新后的用户信息。
            ```json
            {
              "id": 1,
              "username": "currentUser",
              "nickname": "农业小能手",
              "avatarUrl": "/path/to/avatar.jpg",
              "phone": "13900139000",
              "email": "<EMAIL>",
              // ... 其他字段
            }
            ```
        * `400 Bad Request`: 请求参数错误。
        * `401 Unauthorized`: 未登录。

* **`POST /api/users/me/avatar`**
    * **描述**: 更新当前登录用户的头像。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`, `Content-Type: multipart/form-data`
    * **请求体 (Request Body)**: 表单数据，包含一个名为 `avatarFile` 的文件。
    * **响应 (Response)**:
        * `200 OK`: 上传成功。
            ```json
            {
              "message": "头像更新成功",
              "avatarUrl": "/new_path/to/avatar.jpg"
            }
            ```
        * `400 Bad Request`: 文件格式或大小错误。
        * `401 Unauthorized`: 未登录。

* **`POST /api/users/me/change-password`**
    * **描述**: 修改当前登录用户的密码。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **请求体 (Request Body)**:
        ```json
        {
          "currentPassword": "oldPassword123",
          "newPassword": "newStrongPassword456"
        }
        ```
    * **响应 (Response)**:
        * `200 OK`: 密码修改成功。
            ```json
            {
              "message": "密码修改成功"
            }
            ```
        * `400 Bad Request`: 旧密码错误或新密码格式不符。
        * `401 Unauthorized`: 未登录。

* **`DELETE /api/users/me`**
    * **描述**: 注销当前登录用户的账户。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **响应 (Response)**:
        * `200 OK`: 账户注销成功。
            ```json
            {
              "message": "账户注销成功"
            }
            ```
        * `401 Unauthorized`: 未登录。
        * `500 Internal Server Error`: 注销过程中发生错误。

* **`GET /api/users/me/settings`**
    * **描述**: 获取当前用户的设置。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **响应 (Response)**:
        * `200 OK`:
            ```json
            {
              "userId": 1,
              "darkModeEnabled": false,
              "notificationsEnabled": true
            }
            ```
        * `401 Unauthorized`: 未登录。

* **`PUT /api/users/me/settings`**
    * **描述**: 更新当前用户的设置。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **请求体 (Request Body)**:
        ```json
        {
          "darkModeEnabled": true,
          "notificationsEnabled": false
        }
        ```
    * **响应 (Response)**:
        * `200 OK`: 更新成功，返回更新后的设置。
            ```json
            {
              "userId": 1,
              "darkModeEnabled": true,
              "notificationsEnabled": false
            }
            ```
        * `401 Unauthorized`: 未登录。

### 3. 大棚 (Greenhouses)

* **`POST /api/greenhouses`**
    * **描述**: 新建大棚。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **请求体 (Request Body)**: (对应前端 `createForm`)
        ```json
        {
          "name": "我的番茄大棚",
          "crop": "番茄",
          "area": 50.5, // 数字
          "type": "glass", // 'glass', 'plastic', 'solar', 'multi'
          "remark": "春季种植批次"
        }
        ```
    * **响应 (Response)**:
        * `201 Created`: 创建成功，返回新创建的大棚信息。
            ```json
            {
              "id": 101,
              "userId": 1,
              "name": "我的番茄大棚",
              "crop": "番茄",
              "area": 50.5,
              "type": "glass",
              "remark": "春季种植批次",
              "status": "normal", // 初始状态
              "currentTemperature": 25.0, // 初始或默认值
              "currentHumidity": 60.0,  // 初始或默认值
              "currentLight": 3000.0, // 初始或默认值
              "currentSoilHumidity": 45.0, // 初始或默认值
              "createTime": "2025-05-28T10:00:00Z" // ISO 8601 格式
            }
            ```
        * `400 Bad Request`: 请求参数校验失败。
        * `401 Unauthorized`: 未登录。
    * **备注**: `createTime` 在前端是 `Date.now()`，后端存 `created_at`。响应时需要将 `created_at` 格式化。初始环境数据可以设为默认值或 null，等待传感器数据上报。

* **`GET /api/greenhouses`**
    * **描述**: 获取当前登录用户的所有大棚列表。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **查询参数 (Query Parameters)**:
        * `page` (可选, `int`, 默认 0): 页码。
        * `size` (可选, `int`, 默认 10): 每页数量。
        * `sort` (可选, `String`, 例如 `name,asc` 或 `createTime,desc`): 排序字段和方向。
    * **响应 (Response)**:
        * `200 OK`:
            ```json
            {
              "content": [
                {
                  "id": 101,
                  "name": "我的番茄大棚",
                  "crop": "番茄",
                  "status": "normal", // "normal", "warning", "error"
                  "temperature": 26.5, // 对应 current_temperature
                  "humidity": 62.0,    // 对应 current_humidity
                  "createTime": "2025-05-28T10:00:00Z" // 对应数据库 created_at
                },
                {
                  "id": 102,
                  "name": "黄瓜实验棚",
                  "crop": "黄瓜",
                  "status": "warning",
                  "temperature": 28.0,
                  "humidity": 70.0,
                  "createTime": "2025-05-27T14:30:00Z"
                }
              ],
              "pageable": {
                "sort": {
                  "sorted": true,
                  "unsorted": false,
                  "empty": false
                },
                "offset": 0,
                "pageNumber": 0,
                "pageSize": 10,
                "paged": true,
                "unpaged": false
              },
              "totalPages": 1,
              "totalElements": 2,
              "last": true,
              "size": 10,
              "number": 0,
              "sort": {
                "sorted": true,
                "unsorted": false,
                "empty": false
              },
              "numberOfElements": 2,
              "first": true,
              "empty": false
            }
            ```
        * `401 Unauthorized`: 未登录。
    * **备注**: 前端列表展示了 `temperature`, `humidity`, `crop`。这些数据应从 `greenhouses` 表的快照字段获取。

* **`GET /api/greenhouses/{greenhouseId}`**
    * **描述**: 获取指定ID大棚的详细信息。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **路径参数 (Path Variables)**:
        * `greenhouseId` (`BIGINT`): 大棚的ID。
    * **响应 (Response)**:
        * `200 OK`:
            ```json
            {
              "id": 101,
              "name": "我的番茄大棚",
              "crop": "番茄",
              "area": 50.5,
              "type": "glass",
              "remark": "春季种植批次",
              "status": "normal",
              "temperature": 26.5,        // current_temperature
              "humidity": 62.0,           // current_humidity
              "light": 3500.0,            // current_light
              "soilHumidity": 55.0,       // current_soil_humidity
              "createTime": "2025-05-28T10:00:00Z"
              // 还可以包含最近一段时间的传感器数据图表所需数据点，或者让前端单独请求
            }
            ```
        * `401 Unauthorized`: 未登录。
        * `403 Forbidden`: 用户无权访问此大棚。
        * `404 Not Found`: 大棚不存在。

* **`PUT /api/greenhouses/{greenhouseId}`**
    * **描述**: 更新指定ID大棚的信息 (前端 "编辑" 功能，目前提示开发中)。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **路径参数 (Path Variables)**:
        * `greenhouseId` (`BIGINT`): 大棚的ID。
    * **请求体 (Request Body)**: (只包含需要更新的字段)
        ```json
        {
          "name": "我的优质番茄大棚",
          "crop": "优质番茄",
          "area": 60.0,
          "type": "plastic",
          "remark": "夏季第二批"
        }
        ```
    * **响应 (Response)**:
        * `200 OK`: 更新成功，返回更新后的大棚完整信息。
            ```json
            {
              "id": 101,
              "name": "我的优质番茄大棚",
              "crop": "优质番茄",
              // ... 其他所有字段
            }
            ```
        * `400 Bad Request`: 请求参数校验失败。
        * `401 Unauthorized`: 未登录。
        * `403 Forbidden`: 用户无权修改此大棚。
        * `404 Not Found`: 大棚不存在。

* **`DELETE /api/greenhouses/{greenhouseId}`**
    * **描述**: 删除指定ID的大棚。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **路径参数 (Path Variables)**:
        * `greenhouseId` (`BIGINT`): 大棚的ID。
    * **响应 (Response)**:
        * `200 OK` 或 `204 No Content`: 删除成功。
            ```json
            {
              "message": "大棚删除成功"
            }
            ```
        * `401 Unauthorized`: 未登录。
        * `403 Forbidden`: 用户无权删除此大棚。
        * `404 Not Found`: 大棚不存在。
    * **备注**: 删除大棚时，也应考虑其关联的传感器数据如何处理 (级联删除或标记为无效)。

* **`GET /api/greenhouses/{greenhouseId}/sensor-data`**
    * **描述**: 获取指定大棚的历史传感器数据。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here`
    * **路径参数 (Path Variables)**:
        * `greenhouseId` (`BIGINT`): 大棚的ID。
    * **查询参数 (Query Parameters)**:
        * `startTime` (可选, `ISO8601 String`): 开始时间。
        * `endTime` (可选, `ISO8601 String`): 结束时间。
        * `dataType` (可选, `String`, e.g., 'temperature,humidity'): 需要的数据类型，逗号分隔。
        * `interval` (可选, `String`, e.g., '10m', '1h', '1d'): 数据聚合间隔 (例如每10分钟一个点)。
        * `page`, `size` (用于分页)。
    * **响应 (Response)**:
        * `200 OK`:
            ```json
            {
              "content": [
                {
                  "recordedAt": "2025-05-28T10:00:00Z",
                  "temperature": 26.5,
                  "humidity": 62.0,
                  "lightIntensity": 3500.0,
                  "soilHumidity": 55.0
                },
                {
                  "recordedAt": "2025-05-28T10:05:00Z",
                  "temperature": 26.6,
                  "humidity": 62.1,
                  "lightIntensity": 3520.0,
                  "soilHumidity": 55.1
                }
                // ...更多数据点
              ],
              // ...分页信息
            }
            ```
        * `401 Unauthorized`: 未登录。
        * `403 Forbidden`: 用户无权访问此大棚数据。
        * `404 Not Found`: 大棚不存在。
    * **备注**: 此接口用于支持大棚详情页中的历史数据图表展示。实际项目中，传感器数据通常由 IoT 设备通过 MQTT 或 HTTP 直接推送到后端服务，而不是由 App 上传。这个 API 是 App 用来拉取数据的。

### 4. 天气 (Weather)

前端的天气页面数据 (`currentWeather`, `agricultureIndex`, `rainForecast`, `weeklyForecast`, `weatherDetails`) 看起来是调用外部天气服务获取的。后端可以封装一个接口来代理这个请求，好处是可以缓存数据、隐藏第三方 API Key。

* **`GET /api/weather`**
    * **描述**: 获取指定位置的天气信息。
    * **请求头 (Headers)**: `Authorization: Bearer your_jwt_token_here` (可选，如果需要用户身份)
    * **查询参数 (Query Parameters)**:
        * `latitude` (`double`): 纬度。
        * `longitude` (`double`): 经度。
        * (或者 `locationName`: `String`, e.g., "北京市海淀区")
    * **响应 (Response)**:
        * `200 OK`:
            ```json
            {
              "currentLocation": "智慧农场 东区", // 后端可以根据经纬度解析
              "currentWeather": {
                "temperature": 26.3,
                "type": "晴",
                "high": 29,
                "low": 19
              },
              "agricultureIndex": {
                "planting": "good",
                "irrigation": "normal",
                "sunlight": "good"
              },
              "rainForecast": { // 分钟级降水
                "willRain": true,
                "time": 45, // 分钟
                "intensity": "小雨",
                "chartData": [0.2, 0.3, /* ... */] // 120个点，每分钟一个
              },
              "weeklyForecast": [
                { "day": "今天", "date": "12/28", "type": "小雨", "high": 29, "low": 19, "icon": "rain", "color": "#2196F3", "advice": "适宜室内作业" },
                // ... 6 more days
              ],
              "weatherDetails": {
                "visibility": 15, // km
                "humidity": 68,   // %
                "windSpeed": 12,  // km/h
                "feelsLike": 28,  // °C
                "uvIndex": 6,
                "pressure": 1013  // hPa
              }
            }
            ```
        * `400 Bad Request`: 位置参数错误。
        * `503 Service Unavailable`: 外部天气服务不可用或请求失败。

### 5. 作物选项 (Crops - 可选)

前端的作物选项是硬编码的。如果希望这些选项由后端动态管理：

* **`GET /api/crops/options`**
    * **描述**: 获取可种植的作物选项列表。
    * **响应 (Response)**:
        * `200 OK`:
            ```json
            [
              {"value": "tomato", "label": "番茄"},
              {"value": "cucumber", "label": "黄瓜"},
              {"value": "eggplant", "label": "茄子"}
              // ...
            ]
            // 或者简单数组
            // ["番茄", "黄瓜", "茄子"]
            ```

## 三、补充说明

1.  **安全性**:
    * **认证**: 使用 JWT (JSON Web Tokens) 进行无状态认证。
    * **授权**: 确保用户只能访问和修改自己的数据。例如，在处理 `/api/greenhouses/{id}` 时，要校验该 `id` 对应的大棚是否属于当前登录用户。
    * **密码存储**: 使用强哈希算法 (如 BCrypt, SCrypt, Argon2) 存储密码。
    * **输入校验**: 对所有用户输入进行严格校验，防止 SQL 注入、XSS 等攻击。
    * **HTTPS**: 生产环境务必使用 HTTPS。

2.  **错误处理**:
    * 统一的错误响应格式，包含错误码和错误信息。
    * 例如：
        ```json
        {
          "timestamp": "2025-05-28T12:00:00Z",
          "status": 404,
          "error": "Not Found",
          "message": "Greenhouse with ID 999 not found",
          "path": "/api/greenhouses/999"
        }
        ```

3.  **分页与排序**:
    * 对于列表数据 (如大棚列表、传感器历史数据)，应支持分页和排序。

4.  **数据更新**:
    * 大棚的实时环境数据 (`current_temperature` 等) 应由传感器数据采集服务在接收到新数据时更新到 `greenhouses` 表中，同时将原始数据存入 `greenhouse_sensor_data` 表。

5.  **事务管理**:
    * 在涉及多个数据表更新的操作中 (如创建大棚并初始化某些关联数据)，应使用数据库事务确保数据一致性。

6.  **文件上传**:
    * 用户头像上传需要专门的处理，通常存储到文件服务器或云存储 (如 S3, OSS)，数据库中只存URL。

这个设计应该能覆盖您前端展示的大部分功能。在实际开发中，可以根据具体需求进行调整和细化。祝您项目顺利！