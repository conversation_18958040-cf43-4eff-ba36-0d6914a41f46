package me.zxk.smartagriculture.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.zxk.smartagriculture.entity.Greenhouse;
import me.zxk.smartagriculture.entity.User;
import me.zxk.smartagriculture.entity.UserSettings;
import me.zxk.smartagriculture.repository.GreenhouseRepository;
import me.zxk.smartagriculture.repository.UserRepository;
import me.zxk.smartagriculture.repository.UserSettingsRepository;
import me.zxk.smartagriculture.util.PasswordUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 数据初始化服务
 * 在应用启动时初始化一些测试数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final UserSettingsRepository userSettingsRepository;
    private final GreenhouseRepository greenhouseRepository;

    @Override
    @Transactional
    public void run(String... args) {
        try {
            initTestData();
        } catch (Exception e) {
            log.error("数据初始化失败: {}", e.getMessage());
        }
    }

    /**
     * 初始化测试数据
     */
    private void initTestData() {
        // 检查是否已有数据
        if (userRepository.count() > 0) {
            log.info("数据库已有数据，跳过初始化");
            return;
        }

        log.info("开始初始化测试数据...");

        // 创建测试用户
        User testUser = createTestUser();
        
        // 创建用户设置
        createUserSettings(testUser.getId());
        
        // 创建测试大棚
        createTestGreenhouses(testUser.getId());

        log.info("测试数据初始化完成");
    }

    /**
     * 创建测试用户
     */
    private User createTestUser() {
        User user = new User();
        user.setUsername("testuser");
        user.setPasswordHash(PasswordUtil.encode("123456"));
        user.setNickname("测试用户");
        user.setPhone("13800138000");
        user.setEmail("<EMAIL>");
        user.setLevel(1);
        user.setExperience(100);
        user.setTotalArea(new BigDecimal("50.0"));
        user.setCropCount(3);
        user.setHarvestCount(5);

        User savedUser = userRepository.save(user);
        log.info("创建测试用户: {}", savedUser.getUsername());
        return savedUser;
    }

    /**
     * 创建用户设置
     */
    private void createUserSettings(Long userId) {
        UserSettings settings = new UserSettings();
        settings.setUserId(userId);
        settings.setDarkModeEnabled(false);
        settings.setNotificationsEnabled(true);

        userSettingsRepository.save(settings);
        log.info("创建用户设置: userId={}", userId);
    }

    /**
     * 创建测试大棚
     */
    private void createTestGreenhouses(Long userId) {
        // 大棚1
        Greenhouse greenhouse1 = new Greenhouse();
        greenhouse1.setUserId(userId);
        greenhouse1.setName("1号智能温室");
        greenhouse1.setCrop("番茄");
        greenhouse1.setArea(new BigDecimal("50.0"));
        greenhouse1.setType("glass");
        greenhouse1.setRemark("主要用于番茄种植实验");
        greenhouse1.setStatus("normal");
        greenhouse1.setCurrentTemperature(new BigDecimal("25.5"));
        greenhouse1.setCurrentHumidity(new BigDecimal("65.0"));
        greenhouse1.setCurrentLight(new BigDecimal("3500.0"));
        greenhouse1.setCurrentSoilHumidity(new BigDecimal("55.0"));
        greenhouseRepository.save(greenhouse1);

        // 大棚2
        Greenhouse greenhouse2 = new Greenhouse();
        greenhouse2.setUserId(userId);
        greenhouse2.setName("2号塑料大棚");
        greenhouse2.setCrop("黄瓜");
        greenhouse2.setArea(new BigDecimal("30.0"));
        greenhouse2.setType("plastic");
        greenhouse2.setRemark("黄瓜种植区域");
        greenhouse2.setStatus("warning");
        greenhouse2.setCurrentTemperature(new BigDecimal("28.0"));
        greenhouse2.setCurrentHumidity(new BigDecimal("70.0"));
        greenhouse2.setCurrentLight(new BigDecimal("3200.0"));
        greenhouse2.setCurrentSoilHumidity(new BigDecimal("60.0"));
        greenhouseRepository.save(greenhouse2);

        // 大棚3
        Greenhouse greenhouse3 = new Greenhouse();
        greenhouse3.setUserId(userId);
        greenhouse3.setName("有机蔬菜棚");
        greenhouse3.setCrop("茄子");
        greenhouse3.setArea(new BigDecimal("20.0"));
        greenhouse3.setType("solar");
        greenhouse3.setRemark("有机蔬菜种植");
        greenhouse3.setStatus("normal");
        greenhouse3.setCurrentTemperature(new BigDecimal("24.0"));
        greenhouse3.setCurrentHumidity(new BigDecimal("62.0"));
        greenhouse3.setCurrentLight(new BigDecimal("3800.0"));
        greenhouse3.setCurrentSoilHumidity(new BigDecimal("52.0"));
        greenhouseRepository.save(greenhouse3);

        log.info("创建测试大棚: 共3个");
    }
}
