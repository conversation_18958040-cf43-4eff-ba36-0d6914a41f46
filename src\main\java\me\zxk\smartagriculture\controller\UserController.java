package me.zxk.smartagriculture.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import me.zxk.smartagriculture.common.ApiResponse;
import me.zxk.smartagriculture.dto.user.*;
import me.zxk.smartagriculture.service.UserService;
import me.zxk.smartagriculture.service.FileUploadService;
import me.zxk.smartagriculture.util.JwtUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户控制器
 */
@Tag(name = "用户管理", description = "用户信息管理、头像上传、密码修改等API")
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private final UserService userService;
    private final FileUploadService fileUploadService;
    private final JwtUtil jwtUtil;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ApiResponse<UserInfoResponse> getCurrentUserInfo(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            UserInfoResponse response = userService.getUserInfo(userId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }

    /**
     * 更新当前用户信息
     */
    @PutMapping("/me")
    public ApiResponse<UserInfoResponse> updateCurrentUserInfo(
            @Valid @RequestBody UpdateUserRequest request,
            HttpServletRequest httpRequest) {
        try {
            Long userId = getUserIdFromRequest(httpRequest);
            UserInfoResponse response = userService.updateUserInfo(userId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }

    /**
     * 上传用户头像
     */
    @PostMapping("/me/avatar")
    public ApiResponse<String> uploadAvatar(
            @RequestParam("avatarFile") MultipartFile file,
            HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            String avatarUrl = fileUploadService.uploadAvatar(file, userId);
            return ApiResponse.success("头像更新成功", avatarUrl);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/me/change-password")
    public ApiResponse<Void> changePassword(
            @Valid @RequestBody ChangePasswordRequest request,
            HttpServletRequest httpRequest) {
        try {
            Long userId = getUserIdFromRequest(httpRequest);
            userService.changePassword(userId, request);
            return ApiResponse.success("密码修改成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }

    /**
     * 删除当前用户账户
     */
    @DeleteMapping("/me")
    public ApiResponse<Void> deleteCurrentUser(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            userService.deleteUser(userId);
            return ApiResponse.success("账户注销成功", null);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }

    /**
     * 获取用户设置
     */
    @GetMapping("/me/settings")
    public ApiResponse<UserSettingsResponse> getUserSettings(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            UserSettingsResponse response = userService.getUserSettings(userId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }

    /**
     * 更新用户设置
     */
    @PutMapping("/me/settings")
    public ApiResponse<UserSettingsResponse> updateUserSettings(
            @Valid @RequestBody UpdateUserSettingsRequest request,
            HttpServletRequest httpRequest) {
        try {
            Long userId = getUserIdFromRequest(httpRequest);
            UserSettingsResponse response = userService.updateUserSettings(userId, request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }

    /**
     * 从请求中获取用户ID
     */
    private Long getUserIdFromRequest(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 从请求中提取JWT Token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        throw new RuntimeException("Token not found");
    }
}
