package me.zxk.smartagriculture.dto.greenhouse;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 创建大棚请求DTO
 */
@Data
public class CreateGreenhouseRequest {

    @NotBlank(message = "大棚名称不能为空")
    @Size(max = 100, message = "大棚名称长度不能超过100个字符")
    private String name;

    @Size(max = 50, message = "作物名称长度不能超过50个字符")
    private String crop;

    @DecimalMin(value = "0.01", message = "大棚面积必须大于0")
    private BigDecimal area;

    @Size(max = 50, message = "大棚类型长度不能超过50个字符")
    private String type;

    private String remark;
}
