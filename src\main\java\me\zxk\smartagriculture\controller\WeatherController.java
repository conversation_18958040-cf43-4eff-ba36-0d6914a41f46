package me.zxk.smartagriculture.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.zxk.smartagriculture.common.ApiResponse;
import me.zxk.smartagriculture.dto.weather.WeatherResponse;
import me.zxk.smartagriculture.service.WeatherService;
import org.springframework.web.bind.annotation.*;

/**
 * 天气控制器
 */
@Tag(name = "天气服务", description = "获取天气信息API")
@RestController
@RequestMapping("/api/weather")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
public class WeatherController {

    private final WeatherService weatherService;

    /**
     * 获取天气信息
     */
    @Operation(summary = "获取天气信息", description = "根据经纬度获取当前天气信息")
    @GetMapping
    public ApiResponse<WeatherResponse> getWeatherInfo(
            @Parameter(description = "纬度", required = true, example = "39.9042") @RequestParam Double latitude,
            @Parameter(description = "经度", required = true, example = "116.4074") @RequestParam Double longitude) {
        try {
            WeatherResponse response = weatherService.getWeatherInfo(latitude, longitude);
            return ApiResponse.success(response);
        } catch (Exception e) {
            return ApiResponse.error(503, "天气服务异常: " + e.getMessage());
        }
    }
}
